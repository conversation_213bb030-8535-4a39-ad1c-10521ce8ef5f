import {
    EccoDate,
    EccoDateTime,
    EventBus,
    EventHandler,
    HateoasResource,
    NumberToObjectMap,
    ReloadEvent
} from "@eccosolutions/ecco-common";
import {Reducer, useEffect, useMemo, useReducer, useState} from "react";
import {useServicesContext} from "../ServicesContext";
import {
    Address,
    Agency,
    ApiClient,
    Building,
    DemandScheduleDto,
    EvidenceGroup,
    fullAddress,
    getRelation,
    Individual,
    OccupancyDto,
    OccupancyFilter,
    PersonUserSummary,
    ProgressState,
    ServiceRecipientRepository,
    SessionData,
    StaffDto,
    TaskNames,
    WorkersRepository
} from "ecco-dto";
import {
    DemandScheduleTaskDto,
    DemandScheduleTaskHandleDto,
    REL_DEMAND_SCHEDULE,
    REL_DEMAND_SCHEDULE_TASK_HANDLES,
    Rota,
    RotaDto,
    RotaRepository
} from "ecco-rota";
import {loadChunks, processLinear} from "./entityLoaders";
import {UserAccessAuditCommand} from "ecco-commands";
import {SourceAudit} from "ecco-dto/evidence/evidence-command-dto";

declare const newrelic: {
    noticeError: (error: Error) => void;
} | undefined

/** Notify error to monitoring platform if possible */
/*
const notifyError = (error: Error) => {
    if (newrelic) {
        newrelic.noticeError(error);
    }
}
*/

/** const [count, inc] = useCounter(); */
export function useCounter() {
    return useReducer(c => c + 1, 0) as [number, () => void];
}

export function useEventHandler<T>(bus: EventBus<T>, handler: EventHandler<T>) {
    useEffect(() => {
        bus.addHandler(handler);
        return () => {
            bus.removeHandler(handler);
        }
    }, []);
}

/** Have this component add/remove handler for reload events */
export function useReloadHandler(handler: () => void) {
    useEventHandler(ReloadEvent.bus, handler);
}

/** NOTE: Requires React v18 and ReactDOM.createRoot(..).
 *
 * Wrap a promise such that suspense handles loading and error.
 *
 * Instead of:
 *     <pre>const {loading, error, data} = usePromise(p)</pre>
 * we instead do
 *     <pre>const data = wrapPromise(p).read()</pre>
 * and this will either return the data, throw an exception or throw a promise that Suspense can use to
 * know when the data has arrived and can re-render.
 * @see https://www.telerik.com/blogs/render-as-you-fetch-with-react-suspense*/
export function promiseToSuspendedResult<T>(promise: Promise<T>): {read: () => T} {
    let status: "pending" | "success" | "error" = "pending";
    let result: T;
    let suspender = promise.then(
        (r) => {
            status = "success";
            result = r;
        },
        (e) => {
            status = "error";
            result = e;
        }
    );
    return {
        read(): T {
            switch (status) {
                case "pending": throw suspender
                case "success": return result
                case "error": throw result
            }
        }
    };
}

/**
 * Allows us to either get all resolved or loading or error rather than numerous loading/error possibilities
 * <pre>
 *   const {resolved, loading} = usePromises([
 *       () => Promise.resolve("s"),
 *       () => Promise.resolve(1)
 *   ])
 *   resolved && resolved.a.toUpperCase()
 *   resolved && resolved.b.toFixed()
 </pre>
 * @param promiseFactories e.g. [() => Promise.resolve("s"), () => Promise.resolve(1)]
 * @param deps inputs that would trigger a reload
 */
export function usePromises<T1, T2, T3>(
    promiseFactories: readonly [() => Promise<T1>, () => Promise<T2>, () => Promise<T3>],
    deps?: any[] | undefined
): {resolved?: [T1, T2, T3] | undefined; error: any; loading: boolean};

export function usePromises<T1, T2>(
    promiseFactories: readonly [() => Promise<T1>, () => Promise<T2>],
    deps?: any[] | undefined
): {resolved?: [T1, T2] | undefined; error: any; loading: boolean};

export function usePromises<T1>(
    promiseFactories: readonly [() => Promise<T1>],
    deps?: any[] | undefined
): {resolved?: [T1] | undefined; error: any; loading: boolean};

export function usePromises<A>(
    promiseFactories: readonly (() => Promise<A>)[],
    deps: any[] = []
): {resolved?: A[] | undefined; error: any; loading: boolean} {
    return usePromise(() => {
        const promises = promiseFactories.map(p => p());
        // see sequentialMapAll and DEV-2448
        // Promise.all floods requests to the browser, as does reduce, unless a method generates the promise (see d37ab938)
        return Promise.all(promises);
    }, deps);
}

// State and actions for usePromise reducer
interface PromiseState<R> {
    loading: boolean;
    resolved?: R;
    error: any;
}

type PromiseAction<R> =
    | { type: 'LOADING' }
    | { type: 'RESOLVED'; payload: R }
    | { type: 'ERROR'; payload: any }
    | { type: 'RESET' };

function promiseReducer<R>(state: PromiseState<R>, action: PromiseAction<R>): PromiseState<R> {
    switch (action.type) {
        case 'LOADING':
            return { loading: true, resolved: undefined, error: undefined };
        case 'RESOLVED':
            return { loading: false, resolved: action.payload, error: undefined };
        case 'ERROR':
            return { loading: false, resolved: undefined, error: action.payload };
        case 'RESET':
            return { loading: true, resolved: undefined, error: undefined };
        default:
            return state;
    }
}

/**
 * Have a promise handle/return resolved, error, loading and reload.
 *   - resolved - if the data is loaded
 *   - error - if there is an error
 *   - loading - if its currently loading (initially true)
 *   - reload - specifically call a reload
 * The default bus, ReloadEvent.bus, allows the promise to add itself as a handler to a ReloadEvent.bus.fire().
 * The handler simply increments a counter causing the effect dependencies to change, and therefore reload.
 * However, the idea is to provide your own event bus so that we're not reloading the world, where the bus
 * represents the components that require the data - see useLatestCommands and useWorkflow.
 *
 * Optimized version using useReducer to batch state updates and minimize re-renders.
 */
export function usePromise<R>(
    promiseFactory: () => Promise<R>,
    deps: any[] = [],
    bus: EventBus<any> = ReloadEvent.bus
): {resolved?: R | undefined; error: any; loading: boolean; reload: () => void} {
    const [state, dispatch] = useReducer(promiseReducer<R>, {
        loading: true,
        resolved: undefined,
        error: undefined
    });
    const [v, incV] = useCounter();

    useEffect(() => {
        const depsMsg = `usePromise() deps: [${deps.map(d => String(d)).toString()}]`; // String(d) is so we see null and undefined instead of them disappearing
        if (depsMsg.includes("[object")) {
            throw new Error("Cannot use object (eg array) with default toString() in " + depsMsg); // toString() must return an invariant for that object
        } else {
            console.debug(depsMsg);
        }
        const abort = new AbortController();

        // Single state update to start loading
        dispatch({type: "LOADING"});

        promiseFactory()
            .then(r => {
                if (!abort.signal.aborted) {
                    // Single state update with resolved data
                    dispatch({type: "RESOLVED", payload: r});
                }
            })
            .catch(e => {
                if (!abort.signal.aborted) {
                    // Single state update with error
                    dispatch({type: "ERROR", payload: e});
                }
                console.warn(e);
                // TODO: Need to test this ->: notifyError(e); // Notify so we may beat the user to it
            });

        bus.addHandler(incV);
        return () => {
            bus.removeHandler(incV);
            abort.abort();
        };
    }, [...deps, v]);

    return {
        resolved: state.resolved,
        error: state.error,
        loading: state.loading,
        reload: incV
    };
}

/**
 * React hook to help with Components that take a mutable value as a prop.
 *
 * Returns a function that, when called, forces the *current* Component to
 * update. You should call this function whenever the mutable value changes,
 * unless the change is already reported by one of the optional EventBuses.
 *
 * Optionally takes an array of EventBuses. Whenever any event is fired on
 * any of the buses, the mutable value is assumed to have been modified, and
 * the *current* Component is forced to update, equivalent to calling the
 * update function described above.
 *
 * Only the current Component is forced to update. If the mutable value is
 * passed through to any child Components, then those Components should also
 * call useMutable.
 *
 * @example
 * // Activity fires an event on changeEventBus when it changes.
 * useMutable(activity, [activity.changeEventBus]);
 *
 * // User must call forceUpdate when mutableValue changes.
 * const forceUpdate = useMutable(mutableValue);
 *
 * @param mutable The mutable value itself.
 * @param updateBuses An optional array of EventBuses that report updates
 * to the mutable value.
 * @returns A function that, when called, forcibly updates the current
 * Component.
 */
export function useMutable<T>(mutable: T, updateBuses: readonly EventBus<unknown>[] = []): () => void {
    const [_v, forceUpdate] = useCounter();

    useEffect(
        () => {
            updateBuses.forEach(bus => bus.addHandler(forceUpdate));
            return () => updateBuses.forEach(bus => bus.removeHandler(forceUpdate));
        },
        [mutable, updateBuses] // mutable here because it is expected to be the owner of the update buses
    );

    return forceUpdate;
}


/**
 * CommandSource requires access to init/state, but it acts as a closure.
 * We could register a new CommandSource each time, but that would be awkward also
 * We use the trick from UserForm/TaskForm where we create a memo object
 * so that the reference to that object does not change.
 * There are a number of other approaches - such as chaining reducers/dispatchers
 * - see https://stackoverflow.com/questions/59200785/react-usereducer-how-to-combine-multiple-reducers
 */
export function useCommandSourceStateHolder<T>(initStateFromDefault: T) {
    // We use this fixed object to hold the state we're mutating
    type Entity = Partial<T>;
    type StateHolderReducer = Reducer<T, Entity>;
    const stateHolderMemo = useMemo<{state: T}>(
            () => ({
                state: {...initStateFromDefault}
            }),
            []
    );
    const reducerHolder: StateHolderReducer = (_prevState, action) => {
        stateHolderMemo.state = {...stateHolderMemo.state, ...action};
        return stateHolderMemo.state;
    };

    // const reducerHolder = useCallback((prevState, action) => {
    //     // stateHolderMemo.state = {...stateHolderMemo.state, ...action};
    //     // return stateHolderMemo.state;
    //     return prevState;
    // }, []);

    const [stateHolder, dispatchHolder] = useReducer<StateHolderReducer>(
            reducerHolder,
            stateHolderMemo.state
    );

    return {stateHolderMemo, stateHolder, dispatchHolder};
}

/**
 * This could probably do with some error handling.
 * @param workerId
 * @return resolved StaffDto or null while loading.
 */
export function useStaff(workerId: number) {
    const {workersRepository} = useServicesContext();
    // noinspection JSUnusedLocalSymbols
    const {
        resolved: staff,
        error,
        loading
    } = usePromise(
        () => (!workerId ? Promise.resolve(null) : workersRepository.findOneWorker(workerId)),
        [workerId]
    );
    return {staff: staff, error, loading};
}

export function useWorkersWithSameAccess(excludeMe = true, role = "ROLE_STAFF") {
    const {sessionData, workersRepository} = useServicesContext();
    const {resolved, error, loading} = usePromise(
        () =>
            workersRepository
                .findWorkersWithSameAccess(sessionData, excludeMe, role)
                .then(workersWithAccess => workersWithAccess.filter(w => !w.disabled)),
        []
    );
    return {staffWithSameAccess: resolved, error, loading};
}

export function useWorkersWithAccessTo(serviceId: number | null, role: string) {
    const {workersRepository} = useServicesContext();
    const {resolved, error, loading} = usePromise<PersonUserSummary[]>(
        () =>
            serviceId != null
                ? workersRepository
                      .findWorkersWithAccessTo(serviceId, undefined, role)
                      .then(workersWithAccess => workersWithAccess.filter(w => !w.disabled))
                : Promise.resolve([] as PersonUserSummary[]),
        [serviceId, role]
    );
    return {workersWithAccessTo: resolved, error, loading};
}

export function getWorkersWithAccessToFileOrSameAccessAsMe(
    sessionData: SessionData,
    workersRepository: WorkersRepository,
    serviceAllocationId: number,
    role: string,
    excludeMe: boolean
) {
    const svcCat = sessionData.getServiceCategorisation(serviceAllocationId);
    const limitToFile = sessionData.isEnabled("decideFinal.useAccessToFile");
    const users = limitToFile
        ? workersRepository.findWorkersWithAccessTo(svcCat.serviceId, svcCat.projectId, role)
        : workersRepository.findWorkersWithSameAccess(sessionData, excludeMe, role);
    return users.then(workersWithAccess => workersWithAccess.filter(w => !w.disabled));
}

export function useWorkersWithAccessToFileOrSameAccess(
    serviceAllocationId: number | null,
    role: string,
    excludeMe = true
) {
    const {sessionData, workersRepository} = useServicesContext();

    const {resolved, error, loading} = usePromise<PersonUserSummary[]>(
        () =>
            serviceAllocationId != null
                ? getWorkersWithAccessToFileOrSameAccessAsMe(
                      sessionData,
                      workersRepository,
                      serviceAllocationId,
                      role,
                      excludeMe
                  )
                : Promise.resolve([] as PersonUserSummary[]),
        [serviceAllocationId, role]
    );
    return {workers: resolved, error, loading};
}

// potentially in workersRepository, but that's in ecco-dto without loadChunks etc
function findWorkersWithAccessToMany(
        workersRepository: WorkersRepository,
        serviceIds: number[],
        role = "ROLE_STAFF"
): Promise<PersonUserSummary[]> {

    const singlePromise = (id: number) =>
            workersRepository.findWorkersWithAccessTo(id, undefined, role);

    const singleChunkedPromise = (ids: number[]) =>
            processLinear(ids, singlePromise)

    return loadChunks(
            serviceIds,
            singleChunkedPromise
    );
}

export function useWorkersWithAccessToEmployedAt(
    serviceIds: null | number[],
    employedAt: EccoDate,
    role: string
) {
    const {workersRepository} = useServicesContext();

    const workersWithRelevantJobsQ = (workers: PersonUserSummary[]) => {
        const singlePromise = (ids: number[]) =>
            workersRepository.findWorkersEmployedAtByIndividualIds(ids, employedAt);
        return loadChunks(
            workers.map(c => c.individualId),
            singlePromise
        );
    };

    const uniqueWorkers = (workersWithDuplicates: PersonUserSummary[]) => {
        //const uniqIds = [...new Set(workersWithDuplicates.map(item => item.individualId))]
        const seen = new Set();
        const unique = workersWithDuplicates.filter(item => {
            const duplicate = seen.has(item.individualId);
            seen.add(item.individualId);
            return !duplicate;
        });
        return unique;
    };

    const {resolved, error, loading} = usePromise<StaffDto[]>(
        () =>
            serviceIds != null
                ? // NB cached
                  findWorkersWithAccessToMany(workersRepository, serviceIds!, role)
                      .then(workersWithAccess => workersWithAccess.filter(w => !w.disabled))
                      // NB the workers can span many services, so get the unique list
                      .then(uniqueWorkers)
                      // NB NOT cached
                      .then(workersWithAccess => workersWithRelevantJobsQ(workersWithAccess))
                : Promise.resolve([] as StaffDto[]),
        []
    );

    return {workersWithAccessToEmployedAt: resolved, error, loading};
}

/*const TextRegex = /([A-Za-z]+)/;

function toSortKey(line: string) {
    const aMatch = TextRegex.exec(line);
    return aMatch && aMatch.length > 0 ? aMatch[0] : line;
}*/

function compareAddress(a?: Address | undefined, b?: Address | undefined) {
    if (a == null) {
        return b == null ? 0 : -1;
    }
    if (b == null) {
        return 1;
    }

    const postcode = a.postcode.localeCompare(b.postcode);
    if (postcode != 0) return postcode;

    let a1 = a.address[0]!,
        b1 = b.address[0]!;
    // TODO: If starts with a number, sort numerically else alphabetically (alpha first for named houses)
    // a1 = toSortKey(a1);
    // b1 = toSortKey(b1);

    return a1.localeCompare(b1);
}
function sortByStreet(buildings: Building[]) {
    return buildings.sort( (a,b) => compareAddress(a.address, b.address));
}
function sortByName(buildings: Building[]) {
    return buildings.sort((a, b) => (a.name || "").localeCompare(b.name || ""));
}
function sortByParentNameThenName(buildings: Building[]) {
    return buildings.sort((a, b) => {
        const parent = (a.parentName || "").localeCompare(b.parentName || "");
        const name = (a.name || "").localeCompare(b.name || "");
        return parent || name;
    });
}
function sortByChanges(buildings: Building[]) {
    return buildings.sort((a, b) => {
        // Get the most recent occupancy change date for each building
        const getLatestChangeDate = (building: Building): Date => {
            if (!building.occupancy || building.occupancy.length === 0) {
                return new Date(0); // Very old date for buildings with no history
            }

            // Find the most recent validFrom or validTo date
            // excluding voids, because they are not a change
            // and otherwise voids use the reporting end date - so always show at the top
            let latestDate = new Date(0);
            building.occupancy.forEach(occupancy => {
                // skip voids - they are gap fillers so something real is there anyway
                if (occupancy.serviceRecipientId == null) {
                    return;
                }
                const validFrom = new Date(occupancy.validFrom);
                if (validFrom > latestDate) {
                    latestDate = validFrom;
                }
                if (occupancy.validTo) {
                    const validTo = new Date(occupancy.validTo);
                    if (validTo > latestDate) {
                        latestDate = validTo;
                    }
                }
            });
            return latestDate;
        };

        const dateA = getLatestChangeDate(a);
        const dateB = getLatestChangeDate(b);
        return dateB.getTime() - dateA.getTime(); // Most recent first
    });
}
export function useIncidentBySrId(serviceRecipientId: number | undefined) {
    const {incidentsRepository} = useServicesContext();
    const {resolved, error, loading, reload} = usePromise(
        () =>
            !serviceRecipientId
                ? Promise.resolve(null)
                : incidentsRepository.findByServiceRecipientId(serviceRecipientId),
        [serviceRecipientId]
    );
    return {incident: resolved, error, loading, reload};
}

export function useRepairBySrId(serviceRecipientId: number | undefined) {
    const {repairsRepository} = useServicesContext();
    const {resolved, error, loading, reload} = usePromise(
        () =>
            !serviceRecipientId
                ? Promise.resolve(null)
                : repairsRepository.findByServiceRecipientId(serviceRecipientId),
        [serviceRecipientId]
    );
    return {repair: resolved, error, loading, reload};
}

export function useRepairRates() {
    const {repairsRepository} = useServicesContext();
    const ratesQ = useMemo(() => repairsRepository.findRates(), []);
    const {resolved, error, loading, reload} = usePromise(() => ratesQ, []);
    return {rates: resolved, error, loading, reload};
}

/** All buildings for user */
export function useBuildings(primaryOnly = true, onCondition = true) {
    const {getBuildingRepository} = useServicesContext();
    const showChildren = primaryOnly ? "false" : "true";
    const {resolved, error, loading} = usePromise(
        () =>
            onCondition
                ? getBuildingRepository().findAllBuildingsForUser({showChildren}).then(sortByStreet)
                : Promise.resolve(null),
        [showChildren]
    );
    return {buildings: resolved, error, loading};
}

export function useBuilding(buildingId: number | undefined) {
    const {getBuildingRepository} = useServicesContext();
    const {resolved, error, loading} = usePromise(
        !buildingId
            ? () => Promise.resolve(undefined)
            : () => getBuildingRepository().findOneBuilding(buildingId),
        [buildingId]
    );
    return {building: resolved, error, loading};
}

export function useAddress(addressId: number | undefined) {
    const {getAddressRepository} = useServicesContext();
    const {resolved, error, loading} = usePromise(
        !addressId
            ? () => Promise.resolve(undefined)
            : () => getAddressRepository().findOneAddress(addressId),
        [addressId]
    );
    return {address: resolved, error, loading};
}

export function useBuildingCareRuns(buildingId: number) {
    const {getBuildingRepository} = useServicesContext();
    const {resolved, error, loading} = usePromise(
        () => getBuildingRepository().findAllBuildingCareRuns(buildingId),
        [buildingId]
    );
    return {careruns: resolved, error, loading};
}

export function useClient(clientId?: number | null | undefined) {
    const {clientRepository} = useServicesContext();
    const {resolved, error, loading} = usePromise(
        () => (!clientId ? Promise.resolve(null) : clientRepository.findOneClient(clientId)),
        [clientId]
    );
    return {client: resolved, error, loading};
}

export function useAgency(agencyId?: number | null | undefined) {
    const {contactsRepository} = useServicesContext();
    const {resolved, error, loading} = usePromise(
        () => (agencyId ? contactsRepository.findOneAgency(agencyId) : Promise.resolve(null)),
        [agencyId]
    );
    return {agency: resolved, error, loading};
}

export function useIndividual(contactId?: number | null | undefined) {
    const {contactsRepository} = useServicesContext();
    const {resolved, error, loading} = usePromise(
        () => (contactId ? contactsRepository.findOneIndividual(contactId) : Promise.resolve(null)),
        [contactId]
    );
    return {contact: resolved, error, loading};
}

export function useReferralSummaryBySrId(srId: number) {
    const {referralRepository} = useServicesContext();
    const {resolved, error, loading} = usePromise(
        () => referralRepository().findOneReferralSummaryByServiceRecipientIdUsingDto(srId),
        [srId]
    );
    return {referral: resolved, error, loading};
}

export function useReferralBySrId(serviceRecipientId: number | undefined) {
    const {referralRepository} = useServicesContext();
    const {resolved, error, loading, reload} = usePromise(
        () =>
            !serviceRecipientId
                ? Promise.resolve(null)
                : referralRepository().findOneReferralByServiceRecipientId(serviceRecipientId),
        [serviceRecipientId]
    );
    return {referral: resolved, error, loading, reload};
}

/** One of params must be a value */
export function useAddressHistories(
    serviceRecipientId?: number | undefined,
    contactId?: number | undefined
) {
    const {addressHistoryRepository} = useServicesContext();
    const {resolved, error, loading, reload} = usePromise(() => {
        return contactId
            ? addressHistoryRepository.findAllByContactOrderByValidFromDesc(contactId)
            : addressHistoryRepository.findAllByServiceRecipientOrderByValidFromDesc(
                  serviceRecipientId!
              );
    }, [serviceRecipientId, contactId]);
    return {history: resolved, error, loading, reload};
}

// TODO search blur / pretty occupancy links etc / dates (3) / sort by name/adr/changes (or filter 'changes')
// TODO apply to bldg (parent and unit) / finance based on occupancy??
// TODO managed voids 'create' with srId as void
// TODO creative
export type SortOption = "name" | "changes";

export function useBuildingsWithOccupancy(
    from: EccoDate,
    to: EccoDate,
    filter: OccupancyFilter,
    search: string, // UNUSED
    page: number,
    parentBuildingSrId?: number,
    sortOption: SortOption = "name"
) {
    const {getBuildingRepository, serviceRecipientRepository} = useServicesContext();

    const buildingsSize = 20;

    // Check if we should use two-phase loading (when filter is specific, not "all")
    // to get the buildings that are either occupied or void currently
    const shouldUseTwoPhaseLoading = useMemo(() => {
        return filter !== "all" && (filter === "occupied" || filter === "void");
    }, [filter]);

    // PREPARE MATCH
    const matchSearch = useMemo(
        () => (search: string, bldgsWithOccupancy: Building[]) => {
            if (!bldgsWithOccupancy || search.trim() === "") {
                return bldgsWithOccupancy || [];
            }

            const searchTerm = search.toLowerCase().trim();
            return bldgsWithOccupancy.filter(building => {
                // Search in building name
                if (building.name && building.name.toLowerCase().includes(searchTerm)) {
                    return true;
                }

                // Search in building ID
                if (building.buildingId.toString().includes(searchTerm)) {
                    return true;
                }

                // Search in parent building name
                if (building.parentName && building.parentName.toLowerCase().includes(searchTerm)) {
                    return true;
                }

                // Search in address
                if (building.address) {
                    const addressStr = fullAddress(building.address);
                    if (addressStr && addressStr.toLowerCase().includes(searchTerm)) {
                        return true;
                    }
                }

                return false;
            });
        },
        []
    );

    const {resolved, error, loading} = usePromise(() => {
        // GET ALL BUILDINGS, CACHED
        const buildingsAllQ = getBuildingRepository()
            .getCachedBuildingsMap()
            .then(bldgs => Object.values(bldgs));
        //.then(sortByParentName)
        //.then(sortByName);

        // MATCH APPLY TO ALL BUILDINGS
        const useSpecific = !!parentBuildingSrId;
        const buildingsSearchQ = buildingsAllQ.then(bldgs => {
            // TODO should be a hierarchy, but we're only one deep
            //  see BuildingController getFixedContainersHierarchical
            const parentBuildingId = useSpecific
                ? bldgs.filter(b => b.serviceRecipientId == parentBuildingSrId).pop()?.buildingId
                : undefined;
            const matchSpecific = (bldg: Building) =>
                useSpecific && bldg.parentId == parentBuildingId;
            const matchGeneral = (bldg: Building) =>
                !useSpecific && !bldg.disabled && bldg.parentId;
            const matchBldgs = bldgs.filter(b => matchSpecific(b) || matchGeneral(b));
            return matchSearch(search, matchBldgs);
        });

        const buildingsSearchPageQ = buildingsSearchQ.then(filteredBldgs => {
            const start = page * buildingsSize;
            const end = start + buildingsSize;
            return filteredBldgs.slice(start, end);
        });

        // TWO-PHASE LOADING LOGIC
        if (shouldUseTwoPhaseLoading) {
            // PHASE 1: OCCUPANCY NOW FOR MATCHING SEARCH (ie all void/occupied)
            const today = EccoDate.todayLocalTime();
            const phase1OccupancyQ = buildingsSearchPageQ.then(bldgs => {
                return getBuildingRepository().findOccupancy(
                    today,
                    today.addDays(1), // Small range around today
                    filter,
                    null,
                    undefined,
                    bldgs.map(b => b.buildingId)
                );
            });

            // PHASE 1: RESULTS
            const phase2BuildingsQ = Promise.all([buildingsSearchPageQ, phase1OccupancyQ]).then(
                ([bldgs, phase1Occupancy]) => {
                    // Get building IDs that have relevant occupancy today
                    const relevantBuildingIds = new Set(phase1Occupancy.map(o => o.buildingId));
                    // Filter buildings to only those with relevant current occupancy
                    return bldgs.filter(b => relevantBuildingIds.has(b.buildingId));
                }
            );

            // PHASE 2: ALL OCCUPANCY FOR MATCHING PHASE 1
            // NB the server side could be used to load only occupancy history but for now we want the filter for the phase 1 (current)
            const phase2OccupancyQ = phase2BuildingsQ.then(relevantBldgs => {
                if (relevantBldgs.length === 0) {
                    return [];
                }
                return getBuildingRepository().findOccupancy(
                    from,
                    to,
                    "all", // get everything on phase 2 - the 'current' filter is designed for only current/now
                    null,
                    undefined,
                    relevantBldgs.map(b => b.buildingId)
                );
            });

            // Process the two-phase data
            const srQ = phase2OccupancyQ.then(data =>
                processServiceRecipients(data, serviceRecipientRepository)
            );

            return phase2BuildingsQ.then(bldgs => {
                return srQ.then(occupancy => {
                    return processBuildingsWithOccupancy(bldgs, occupancy, sortOption, filter);
                });
            });
        } else {
            // SINGLE-PHASE LOADING (original logic)
            const occupancyQ = buildingsSearchPageQ.then(bldgs => {
                return getBuildingRepository().findOccupancy(
                    from,
                    to,
                    filter,
                    null, // TODO search client name server side since bldg is client side
                    undefined, // no page - find all history between dates
                    bldgs.map(b => b.buildingId)
                );
            });

            const srQ = occupancyQ.then(data =>
                processServiceRecipients(data, serviceRecipientRepository)
            );

            return buildingsSearchPageQ.then(bldgs => {
                return srQ.then(occupancy => {
                    return processBuildingsWithOccupancy(bldgs, occupancy, sortOption, filter);
                });
            });
        }

        // Helper function to process service recipients
        function processServiceRecipients(
            data: OccupancyDto[],
            serviceRecipientRepository: ServiceRecipientRepository
        ) {
            // get referrals for the occupancy data
            const oBySrId = {} as NumberToObjectMap<OccupancyDto[]>;
            const allSrIds: number[] = [];
            data.forEach(o => {
                const exist = oBySrId[o.serviceRecipientId];
                if (exist) {
                    exist.push(o);
                } else {
                    oBySrId[o.serviceRecipientId] = [o];
                    allSrIds.push(o.serviceRecipientId);
                }
            });

            // get service recipient information to display
            // NB this ignores ACLs
            const singleChunkedPromise = (ids: number[]) =>
                serviceRecipientRepository.findManyServiceRecipientByIds(ids).then(svcRec => {
                    svcRec.forEach(s => {
                        const ohMatches = oBySrId[s.serviceRecipientId];
                        if (!ohMatches) {
                            console.error(
                                "no occupancy for service recipient - probably dodgy test data",
                                s
                            );
                            return;
                        } else {
                            ohMatches.forEach(oh => {
                                oh.serviceRecipient = s;
                            });
                        }
                    });
                    return data;
                });

            // we don't need this conversion - it's already accepting number[]
            //const singleChunkedPromise = (ids: number[]) => processLinear(ids, singlePromise);

            return loadChunks(allSrIds, singleChunkedPromise).then(() => data);
        }

        // Helper function to process buildings with occupancy
        function processBuildingsWithOccupancy(
            bldgs: Building[],
            occupancy: OccupancyDto[],
            sortOption: SortOption,
            filter: OccupancyFilter
        ) {
            bldgs.forEach(b => {
                b.occupancy = occupancy.filter(o => o.buildingId == b.buildingId);
            });

            // Apply sorting after occupancy history is attached
            const sortedBldgs =
                sortOption === "changes" ? sortByChanges(bldgs) : sortByParentNameThenName(bldgs);

            // don't show the whole page of buildings if we actually
            // have a filter server-side applied, meaning we only want to see that data
            const filterSortedBuildings = sortedBldgs.filter(b => {
                if (!filter || filter == "all") {
                    return true;
                } else {
                    return b.occupancy && b.occupancy.length > 0;
                }
            });

            return {
                buildings: filterSortedBuildings,
                totalCount: filterSortedBuildings.length
            };
        }
    }, [
        from.formatIso8601(),
        to.formatIso8601(),
        page,
        parentBuildingSrId,
        filter,
        search,
        sortOption,
        shouldUseTwoPhaseLoading
    ]);

    return {
        bldgsWithOccupancy: resolved?.buildings,
        totalCount: resolved?.totalCount || 0,
        error,
        loading
    };
}

export function useEvents(eventUuids: string[] | null) {
    const {calendarRepository} = useServicesContext();
    const {resolved, error, loading} = usePromise(() => {
        if (!eventUuids) {
            return Promise.resolve([]);
        }
        return calendarRepository.fetchEventsById(eventUuids);
    }, [JSON.stringify(eventUuids)])
    return {appointments: resolved, error, loading};
}

/**
 * Hook that returns requests made and resolved since hook was initiated
 */
export function useApiClientProgress(): ProgressState {
    const {apiClient} = useServicesContext()
    const tracker = apiClient.getRequestTracker();
    const initial = useMemo(() => {
        console.debug(tracker.getProgress())
        return tracker.getProgress();
    }, []);
    const [progress, setProgress] = useState<ProgressState>(initial)
    useEventHandler(tracker.bus,
            p => setProgress({count: p!.count - initial.count, total: p!.total - initial.total}));

    return progress;
}

/**
 * @return resolved ContractDto[] or null while loading.
 */
export function useContracts() {
    const {contractRepository} = useServicesContext();
    const {resolved, error, loading} = usePromise(() => contractRepository.findAllContracts());
    return {contracts: resolved, error, loading};
}

export function useContract(contractId: number) {
    const {contractRepository} = useServicesContext();
    const {resolved, error, loading} = usePromise(
        () => contractRepository.findOneContract(contractId),
        [contractId]);
    return {contract: resolved, error, loading};
}

/**
 * @param contractId - The contract ID to fetch rate cards for
 * @return resolved RateCardDto[] or null while loading.
 */
export function useRateCards(contractId: number) {
    const {contractRepository} = useServicesContext();
    const {resolved, error, loading} = usePromise(
        () => contractRepository.findRateCardsForContract(contractId),
        [contractId]);
    return {rateCards: resolved, error, loading};
}

export function useDemandSchedule(agreementId: number, scheduleId: number) {
    const {rotaRepository} = useServicesContext();
    const {resolved, error, loading} = usePromise(
        () => rotaRepository.findScheduleById(agreementId, scheduleId),
        [scheduleId]
    )
    return {schedule: resolved, error, loading}
}

export function directTaskHandlesPromise(
    apiClient: ApiClient,
    resource: HateoasResource
): Promise<DemandScheduleTaskHandleDto[] | undefined> {
    console.assert(apiClient != null);
    if (!resource || !getRelation(resource, REL_DEMAND_SCHEDULE_TASK_HANDLES)) {
        return Promise.resolve(undefined);
    }
    return apiClient.fetchRelations<DemandScheduleTaskHandleDto>(
        [resource],
        REL_DEMAND_SCHEDULE_TASK_HANDLES
    );
}

// LOAD the schedule direct task handles to know the tasks to do on the visit
// NB for offline this solution needs moving server-side
export function useDirectTaskHandles(resource: HateoasResource) {
    const {apiClient} = useServicesContext();
    // noinspection JSUnusedLocalSymbols
    const {resolved, error, loading} = usePromise(() => {
        return directTaskHandlesPromise(apiClient, resource);
    }, []);
    return {directTaskHandles: resolved, error, loading};
}

// load the schedule direct tasks for schedule editing
export function useDirectTasks(
    resource: DemandScheduleDto | undefined | null,
    srId: number,
    group: EvidenceGroup
) {
    const {apiClient, supportSmartStepsSnapshotRepository} = useServicesContext();
    // noinspection JSUnusedLocalSymbols
    const {resolved, error, loading} = usePromise(() => {
        if (!resource) {
            return Promise.resolve([]);
        }
        const taskHandlesQ = directTaskHandlesPromise(apiClient, resource);
        const snapshotQ =
            // we need the future because schedules 'applicable date' will often be the future
            // and we need to return all smart steps, so perhaps date-less would be better, but this is easier
            supportSmartStepsSnapshotRepository.findSupportSmartStepsSnapshotByServiceRecipientIdAndEvidenceGroupAtTime(
                srId,
                group.name,
                EccoDateTime.nowUtc().addYears(99)
            );
        return Promise.all([taskHandlesQ, snapshotQ]).then(([taskHandles, snapshot]) => {
            return !taskHandles
                ? []
                : taskHandles.map(h => {
                      const a = snapshot.latestActions
                          .filter(l => l.actionInstanceUuid == h.taskInstanceId)
                          .pop()!;
                      const t: DemandScheduleTaskDto = {
                          taskInstanceId: a.actionInstanceUuid!,
                          taskText: a.goalName,
                          taskDescription: a.goalPlan,
                          taskDefId: a.actionId,
                          taskDefName: a.name || ""
                      };
                      return t;
                  });
        });
    }, [resource?.scheduleId, srId, group?.name]);
    return {directTasks: resolved, error, loading};
}

export function useNearbyAppointments(calendarId: string) {
    const {calendarRepository} = useServicesContext();
    const {resolved, error, loading} = usePromise(
        () => calendarRepository.nearby(calendarId),
        [calendarId]);
    return {appointments: resolved, error, loading};
}

export function useDemandScheduleRelation(resource: HateoasResource) {
    const {apiClient} = useServicesContext();
    const {resolved, error, loading} = usePromise(() => {
        if (!resource || !getRelation(resource, REL_DEMAND_SCHEDULE)) {
            return Promise.resolve(undefined);
        }
        return apiClient.fetchRelation<DemandScheduleDto>(resource, REL_DEMAND_SCHEDULE);
    }, []);
    return {demandSchedule: resolved, error, loading};
}

export function useVisitDetail(
    serviceRecipientPrefix: string,
    serviceRecipientId: number,
    resource: HateoasResource,
    condition: boolean
) {
    const {apiClient, referralRepository, contactsRepository} = useServicesContext();

    const {resolved, error, loading} = usePromise(() => {
        if (!condition) {
            return Promise.resolve({schedule: undefined, contact: undefined});
        }
        const scheduleQ: Promise<DemandScheduleDto | undefined> =
            resource && getRelation(resource, REL_DEMAND_SCHEDULE)
                ? apiClient.fetchRelation<DemandScheduleDto>(resource, REL_DEMAND_SCHEDULE)
                : Promise.resolve(undefined);
        const indQ =
            serviceRecipientPrefix != "r"
                ? Promise.resolve(undefined)
                : referralRepository()
                      .findOneReferralSummaryByServiceRecipientIdUsingDto(serviceRecipientId)
                      .then(r =>
                          r.supportWorkerId
                              ? // TODO far too many requests - but we do this per visit card - so should cache?
                                contactsRepository.findOneIndividual(r.supportWorkerId)
                              : Promise.resolve(undefined)
                      );
        return scheduleQ.then(s =>
            indQ.then(ind => {
                return {schedule: s, contact: ind};
            })
        );
    }, []);
    return {visitDetail: resolved, error, loading};
}

function findRotaDtoFromResources(
    rotaRepository: RotaRepository,
    startDate: EccoDate | null,
    endDate: EccoDate | null | undefined,
    resourceFilter: string,
    demandFilter: string
) {
    const resourcesSrIdsQ = rotaRepository.findRotaResources(
        startDate,
        endDate,
        resourceFilter,
        demandFilter
    );

    const singlePromise = (id: number) => {
        // reconstruct the resourceFilter but with the specificId not 'all'
        // because the resourceFilter pattern is type:all|specificId - see RotaHandler.java
        const resourceFilterPart = resourceFilter.substring(0, resourceFilter.indexOf(":"));
        const resourceSpecificSrId = `${resourceFilterPart}:${id}`;
        return rotaRepository.findRotaByDate(
            startDate,
            endDate,
            resourceSpecificSrId,
            demandFilter,
            true,
            false
        );
    };

    // build an array of the results - simplest to solve the Rota[] later to a single Rota
    const singleChunkedPromise = (ids: number[]) =>
        processLinear(ids, id => singlePromise(id).then(r => [r]));

    // resolve to a single Rota by concatenating the resources - which is all we need
    return resourcesSrIdsQ.then(resourcesSrIds => {
        return loadChunks(resourcesSrIds, singleChunkedPromise).then(c => {
            return c.reduce((cuml, curr) => {
                return cuml == null
                    ? curr
                    : cuml.merge(resourceFilter, demandFilter, curr.getDto());
            }, null as any as Rota);
        });
    });
}

function findRotaDtoFromDemands(
    rotaRepository: RotaRepository,
    startDate: EccoDate | null,
    endDate: EccoDate | null | undefined,
    resourceFilter: string,
    demandFilter: string | null
) {
    // find demand agreement using the filters (eg buildings and workers)
    return rotaRepository
        .findRotaDemands(startDate, endDate, resourceFilter, demandFilter)
        .then(uniqueSrIds => {
            // for each srId, find their demand, which should be cached
            const singlePromise = (srId: number) => {
                const demandSpecificSrId = `${demandFilter}:${srId}`;
                // would be nice to useRota here, but we resolve each promise, not the whole lot
                return rotaRepository.findRotaByDate(
                    startDate,
                    endDate,
                    resourceFilter,
                    demandSpecificSrId,
                    false,
                    true
                );
            };
            const singleChunkedPromise = (ids: number[]) =>
                processLinear(ids, id => singlePromise(id).then(r => [r]));

            return singleChunkedPromise(uniqueSrIds).then(rotas => {
                return rotas.reduce((cuml, curr) => {
                    if (cuml == null) {
                        return curr;
                    }
                    // NB could probably merge, as above: return cuml == null ? curr : cuml.merge(curr.getDto());
                    cuml.getDto().demandAppointments = cuml
                        .getDto()
                        .demandAppointments.concat(curr.getDto().demandAppointments);
                    return cuml;
                }, null as any as Rota);
            });
        });
}

/**
 * Loads the whole rota, and part of as desired (according to the loadResources, loadDemand).
 *
 * @param startDate - if null, defaults to today at back end
 * @param endDate - if null, defaults to start date at back end
 *
 * @param resourceFilter - required, e.g. workers:all
 * @param demandFilter - if null, then no filtering
 * @param loadResource
 * @param loadDemand
 */
export function useRota(
    startDate: EccoDate,
    endDate: EccoDate | null,
    resourceFilter: string,
    demandFilter: string,
    loadResource = true,
    loadDemand = true
) {
    const {sessionData, rotaRepository, getCommandRepository} = useServicesContext();
    const {resolved, error, loading, reload} = usePromise(() => {
        const resources = !loadResource
            ? Promise.resolve(null)
            : findRotaDtoFromResources(
                  rotaRepository,
                  startDate,
                  endDate,
                  resourceFilter,
                  demandFilter
              );

        const demands = !loadDemand
            ? Promise.resolve(null)
            : findRotaDtoFromDemands(
                  rotaRepository,
                  startDate,
                  endDate,
                  resourceFilter,
                  demandFilter
              );

        const rotaDto = {
            resourceFilter,
            demandFilter,
            startDate: startDate.formatIso8601(),
            endDate: endDate?.formatIso8601(),
            resources: [],
            demandAppointments: []
        } as RotaDto;
        const emptyRota = new Rota(rotaRepository, sessionData, getCommandRepository(), rotaDto);
        // NOTE: Hacky to mutate the rota dto appts, so we ditch the mutated one
        return resources.then(rotaForResources =>
            demands.then(rotaForDemands =>
                rotaForResources != null && rotaForDemands != null
                    ? rotaForResources.merge(resourceFilter, demandFilter, rotaForDemands.getDto())
                    : rotaForResources == null && rotaForDemands == null
                    ? emptyRota
                    : rotaForResources || rotaForDemands
            )
        );
    }, [
        startDate.formatIso8601(),
        endDate?.formatIso8601(),
        resourceFilter,
        demandFilter,
        loadResource,
        loadDemand
    ]);
    if (error) throw error; // FIXME: This could be done in usePromise, and handle with ErrorBoundary
    return {rota: resolved, loading, reload};
}

export function useAgreements(
    startDate: EccoDate,
    endDate: EccoDate,
    resourceFilter: string | undefined,
    demandFilter: string | undefined
) {
    const {rotaRepository, serviceRecipientRepository} = useServicesContext();
    const {resolved, error, loading} = usePromise(() => {
        const valid = startDate && endDate && resourceFilter && demandFilter;
        return !valid
            ? Promise.resolve(null)
            : rotaRepository
                  .findAgreementsByDemandAndScheduleDate(
                      resourceFilter,
                      demandFilter,
                      startDate,
                      endDate
                  )
                  .then(agreements => {
                      const singlePromise = (ids: number[]) =>
                          serviceRecipientRepository
                              .findManyServiceRecipientByIds(ids)
                              .then(srs => {
                                  srs.forEach(sr => {
                                      agreements
                                          .filter(
                                              a =>
                                                  a.getServiceRecipientId() == sr.serviceRecipientId
                                          )
                                          .forEach(a => {
                                              a.setServiceRecipientName(sr.displayName);
                                          });
                                  });
                                  return agreements;
                              });
                      return loadChunks(
                          agreements.map(c => c.getServiceRecipientId()),
                          singlePromise,
                          20
                      );
                  });
    }, [startDate.formatIso8601(), endDate.formatIso8601(), resourceFilter, demandFilter]);
    return {agreements: resolved, error, loading};
}

export function useAgreement(
    agreementId: number,
) {
    const {rotaRepository} = useServicesContext();
    const {resolved, error, loading} = usePromise(
        () => rotaRepository.findAgreementByAgreementId(agreementId),
        [agreementId]);
    return {agreement: resolved, error, loading};
}

export function useRotaLiveLoneWorkerStatus(load: boolean, recentOnly: boolean, deps: any[]) {
    const {rotaRepository} = useServicesContext();
    const {resolved, error, loading} = usePromise(
        () =>
            load
                ? // RotaView uses this to get initial (from start of the day) or recent (last hour)
                  // except 'not recentOnly' was in fact the last hour - which we now put to the day upto now
                  recentOnly
                    ? rotaRepository.findEventSnapshotsFromToUtc(
                          EccoDate.todayUtc().toDateTimeMidnight(),
                          EccoDateTime.nowUtc()
                      )
                    : rotaRepository.findEventSnapshotsTodayUntilNowUtc()
                : Promise.resolve(null),
        [load, ...deps]
    );
    return {rotaLiveEventStatuses: resolved, error, loading};
}

export function useUser(username: string) {
    const {userRepository} = useServicesContext();
    const {resolved, error, loading, reload} = usePromise(
        () => userRepository.findOneUser(username),
        [username]
    );
    return {user: resolved, error, loading, reload};
}

export function useUserByContactId(contactId: number) {
    const {userRepository} = useServicesContext();
    const {resolved, error, loading} = usePromise(
        () => userRepository.findOneByContact(contactId),
        [contactId]);
    return {user: resolved, error, loading};
}

export function useUsersWithoutWorker() {
    const {userRepository} = useServicesContext();
    const {resolved, error, loading} = usePromise(
        () => userRepository.findUsersWithoutWorker(),
        []);
    return {users: resolved, error, loading};
}


export function useGroups() {
    const {userRepository} = useServicesContext();
    const {resolved, error, loading} = usePromise(
        () => userRepository.findGroups(),
        []);
    return {groups: resolved, error, loading};
}

export function useServiceRecipient(srId: number) {
    const {serviceRecipientRepository} = useServicesContext();
    const {resolved, error, loading} = usePromise(
        () => serviceRecipientRepository.findOneServiceRecipientById(srId),
        [srId]
    );
    return {serviceRecipient: resolved, error, loading};
}

export function useServiceRecipientAuditAccess(
    srId?: number | undefined,
    taskName = TaskNames.referralView,
    source?: SourceAudit | undefined
) {
    const {getCommandRepository} = useServicesContext();
    const {resolved, error, loading} = usePromise(
        () =>
            srId
                ? getCommandRepository().sendCommand(
                      UserAccessAuditCommand.create(srId, taskName, "STATUS", source)
                  )
                : Promise.resolve(null),
        [srId]
    );
    return {audited: resolved, error, loading};
}

export function useWork(srId: number, group: EvidenceGroup, workUuid: string | null) {
    const {supportWorkRepository} = useServicesContext();
    const {resolved, error, loading} = usePromise(
        () =>
            workUuid
                ? supportWorkRepository.findOneSupportWorkByWorkUuid(srId, group, workUuid)
                : Promise.resolve(null),
        [srId, group.name, workUuid]
    );
    return {work: resolved, error, loading};
}

export function useTask(taskHandle: string) {
    const {tasksRepository} = useServicesContext();
    const {resolved, error, loading} = usePromise(
            () => tasksRepository.getTask(taskHandle),
            [taskHandle]);
    return {task: resolved, error, loading};
}

export function useFinanceCharges(srId: number) {
    const {financeRepository} = useServicesContext();
    const {resolved, error, loading} = usePromise(
        () => financeRepository.findChargesBySrId(srId),
        [srId]
    );
    return {charges: resolved, error, loading};
}

export function useChart(chartDefinition: string) {
    const {chartRepository} = useServicesContext();

    const {resolved, error, loading} = usePromise(
            () => chartRepository.findChartDefByUuid(chartDefinition),
            [chartDefinition]);
    return {chartDefinitionDto: resolved, error, loading};
}

// mimics supportFromSnapshotQ which calls findSupportSmartStepsSnapshotByServiceRecipientIdAndEvidenceGroup
export function useEvidenceSupportSnapshot(srId: number, group: EvidenceGroup) {
    const {supportSmartStepsSnapshotRepository} = useServicesContext();

    const {resolved, error, loading} = usePromise(
            () => supportSmartStepsSnapshotRepository.findSupportSmartStepsSnapshotByServiceRecipientIdAndEvidenceGroup(srId, group.name),
            [srId, group]);
    return {snapshotDto: resolved, error, loading};
}


function sortCompanyName(a: Agency, b: Agency) {
    return (a.companyName || '').localeCompare(b.companyName);
}

function sortLastNameFirstName(a: Individual, b: Individual) {
    let result = (a.lastName || '').localeCompare(b.lastName);
    if (result != 0) {
        return result;
    }
    return (a.firstName || '').localeCompare(b.firstName);
}

export interface AgencyProfessionals {
    agency: Agency;
    professionals: Individual[];
}
function useAgencies(change: number, contextId?: number | undefined) {
    const {contactsRepository} = useServicesContext();
    const {resolved, error, loading} = usePromise(
        () => contactsRepository.findAllAgencies(contextId),
        [change]
    );
    return {agencies: resolved, error, loading};
}
function useProfessionals(change: number, contextId?: number | undefined) {
    const {contactsRepository} = useServicesContext();
    const {resolved, error, loading} = usePromise(
        () => contactsRepository.findAllProfessionals(contextId),
        [change]
    );
    return {professionals: resolved, error, loading};
}

export function useAgenciesWithProfessionals(
    agencyChange: number,
    professionalChange: number,
    contextId?: number | undefined,
    hideDisabledExceptId?: boolean | number | undefined
) {
    const {agencies} = useAgencies(agencyChange, contextId);
    const {professionals} = useProfessionals(professionalChange, contextId);
    if (!agencies || !professionals) {
        return {agenciesWithProfessionals: undefined, loading: true};
    }

    const hideDisabledExceptIdAgency =
        hideDisabledExceptId &&
        professionals.filter(p => p.contactId == hideDisabledExceptId).pop()?.organisationId;

    const filterDisabledExceptId = (
        c: {archived?: string | null | undefined; contactId?: number | undefined},
        hideDisabledExceptId?: boolean | number | undefined
    ) => {
        const hide =
            hideDisabledExceptId &&
            c.contactId &&
            c.archived &&
            c.contactId != hideDisabledExceptId;
        return !hide;
    };

    const ap = agencies
        .sort(sortCompanyName)
        .filter(a => filterDisabledExceptId(a, hideDisabledExceptIdAgency || hideDisabledExceptId))
        .map(a => {
            const withProfessionals: AgencyProfessionals = {
                agency: a,
                professionals: []
            };
            return withProfessionals;
        });

    professionals
        .sort(sortLastNameFirstName)
        .filter(p => filterDisabledExceptId(p, hideDisabledExceptId))
        .forEach(p => {
            // agency may be hidden, so the professionals are never added
            ap.filter(a => p.organisationId == a.agency.contactId).forEach(ap =>
                ap.professionals.push(p)
            );
        });

    return {agenciesWithProfessionals: ap};
}
